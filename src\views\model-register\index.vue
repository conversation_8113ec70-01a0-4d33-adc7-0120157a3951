<script setup lang="ts">
  import { ref, reactive } from "vue"
  import { ElMessage } from 'element-plus'

  const tableColumnHeader = [
    { label: "模型名称", prop: "modelName", align: "center" },
    { label: "模型ID", prop: "modelId", align: "center" },
    { label: "模型类型", prop: "modelType", align: "center" },
    { label: "注册人", prop: "registerUser", align: "center" },
    { label: "注册时间", prop: "registerTime", align: "center" },
    { label: "模型描述", prop: "description", align: "center" },
    { label: "模块状态", prop: "moduleStatus", align: "center" },
    { label: "可用工作流", prop: "workflows", align: "center" },
  ]

  const state = reactive({
    tableTotal: 0,
    tableList: [
      {
        modelName: '图像识别模型',
        modelId: 'mdl-001',
        modelType: '分类模型',
        registerUser: '张三',
        registerTime: '2023-08-01 10:00:00',
        description: '支持图片分类的深度学习模型',
        moduleStatus: '已注册',
        workflows: '图像处理流程, 智能分析流程'
      },
      {
        modelName: '语音识别模型',
        modelId: 'mdl-002',
        modelType: '识别模型',
        registerUser: '李四',
        registerTime: '2023-08-02 11:30:00',
        description: '语音转文本模型',
        moduleStatus: '未注册',
        workflows: '语音分析流程'
      }
      // ...可添加更多示例...
    ],
    params: {
      page: 1,
      pageSize: 10
    }
  })
  // 表格操作
  const tableChangeSelect = (val: any) => {
    console.log(val)
  }
  // 表格删除
  const tableDelete = (row: any) => {
    ElMessage.success('删除成功')
  }
  // 表格编辑
  const tableEdit = (row: any) => {
    console.log(row)
  }
  function handleFilter({ type, data }) {
    console.log('筛选条件:', data)
    // 调用查询接口
  }


</script>
<template>
  <section class="mode_list p-5">
    <!-- <TableFilter :filterList="filters" :clearable="true" @submit="handleFilter" /> -->
    <!-- 表格 -->
    <TableColumn :data="state.tableList" :total="state.tableTotal" :column="tableColumnHeader"
      :page.sync="state.params.page" :pageSize.sync="state.params.pageSize" @changeSelect="tableChangeSelect">
      <template #setting="{ row }">
        <el-button @click="tableDelete(row)" type="primary" link size="small">删除</el-button>
        <el-button @click="tableEdit(row)" type="primary" link size="small">编辑</el-button>
      </template>
    </TableColumn>

  </section>
</template>
<style lang="scss" scoped></style>
