<template>
  <div
    class="min-h-screen flex items-center justify-center bg-gradient-to-br from-blue-100 via-white to-gray-100 dark:from-gray-900 dark:via-gray-800 dark:to-gray-900 px-4">
    <div class="w-full max-w-md bg-white dark:bg-gray-800 rounded-2xl shadow-xl p-8 space-y-6">
      <h3 class="text-2xl font-bold text-center text-gray-700 dark:text-gray-200">{{ title }}</h3>

      <el-form ref="loginRef" :model="loginForm" :rules="loginRules" class="space-y-4">
        <el-form-item prop="username">
          <el-input v-model="loginForm.username" type="text" size="large" auto-complete="off" placeholder="账号"
            class="!h-12">
            <template #prefix>
              <svg-icon icon-class="user" class="el-input__icon w-5 h-5 text-gray-400" />
            </template>
          </el-input>
        </el-form-item>

        <el-form-item prop="password">
          <el-input v-model="loginForm.password" type="password" size="large" auto-complete="off" placeholder="密码"
            @keyup.enter="handleLogin" show-password class="!h-12">
            <template #prefix>
              <svg-icon icon-class="password" class="el-input__icon w-5 h-5 text-gray-400" />
            </template>
          </el-input>
        </el-form-item>

        <el-form-item prop="code" v-if="captchaEnabled">
          <div class="flex items-center space-x-2 w-full">
            <el-input v-model="loginForm.code" size="large" auto-complete="off" placeholder="验证码"
              @keyup.enter="handleLogin" class="flex-1 !h-12">
              <template #prefix>
                <svg-icon icon-class="validCode" class="el-input__icon w-5 h-5 text-gray-400" />
              </template>
            </el-input>
            <img :src="codeUrl" @click="getCode" class="w-24 h-12 object-cover cursor-pointer rounded border" />
          </div>
        </el-form-item>

        <el-checkbox v-model="loginForm.rememberMe" class="text-sm text-gray-600 dark:text-gray-300">
          记住密码
        </el-checkbox>

        <el-form-item>
          <el-button :loading="loading" size="large" type="primary" class="w-full h-12 rounded-lg shadow transition-all"
            @click.prevent="handleLogin">
            <span v-if="!loading">登 录</span>
            <span v-else>登 录 中...</span>
          </el-button>
        </el-form-item>

        <div class="text-right">
          <router-link v-if="register" to="/register" class="text-sm text-blue-500 hover:underline">
            立即注册
          </router-link>
        </div>
      </el-form>

      <div class="text-center text-xs text-gray-400 dark:text-gray-500 mt-4">
        Copyright © 2018-2025 kzzk All Rights Reserved.
      </div>
    </div>
  </div>
</template>

<script setup>
  import { getCodeImg } from "@/api/login"
  import Cookies from "js-cookie"
  import { encrypt, decrypt } from "@/utils/jsencrypt"
  import useUserStore from '@/store/modules/user'

  const title = import.meta.env.VITE_APP_TITLE
  const userStore = useUserStore()
  const route = useRoute()
  const router = useRouter()
  const { proxy } = getCurrentInstance()

  const loginForm = ref({
    username: "admin",
    password: "admin123",
    rememberMe: false,
    code: "",
    uuid: ""
  })

  const loginRules = {
    username: [{ required: true, trigger: "blur", message: "请输入您的账号" }],
    password: [{ required: true, trigger: "blur", message: "请输入您的密码" }],
    code: [{ required: true, trigger: "change", message: "请输入验证码" }]
  }

  const codeUrl = ref("")
  const loading = ref(false)
  // 验证码开关
  const captchaEnabled = ref(true)
  // 注册开关
  const register = ref(false)
  const redirect = ref(undefined)

  watch(route, (newRoute) => {
    redirect.value = newRoute.query && newRoute.query.redirect
  }, { immediate: true })

  function handleLogin() {
    proxy.$refs.loginRef.validate(valid => {
      if (valid) {
        loading.value = true
        // 勾选了需要记住密码设置在 cookie 中设置记住用户名和密码
        if (loginForm.value.rememberMe) {
          Cookies.set("username", loginForm.value.username, { expires: 30 })
          Cookies.set("password", encrypt(loginForm.value.password), { expires: 30 })
          Cookies.set("rememberMe", loginForm.value.rememberMe, { expires: 30 })
        } else {
          // 否则移除
          Cookies.remove("username")
          Cookies.remove("password")
          Cookies.remove("rememberMe")
        }
        // 调用action的登录方法
        userStore.login(loginForm.value).then(() => {
          const query = route.query
          const otherQueryParams = Object.keys(query).reduce((acc, cur) => {
            if (cur !== "redirect") {
              acc[cur] = query[cur]
            }
            return acc
          }, {})
          router.push({ path: redirect.value || "/", query: otherQueryParams })
        }).catch(() => {
          loading.value = false
          // 重新获取验证码
          if (captchaEnabled.value) {
            getCode()
          }
        })
      }
    })
  }

  function getCode() {
    getCodeImg().then(res => {
      captchaEnabled.value = res.captchaEnabled === undefined ? true : res.captchaEnabled
      if (captchaEnabled.value) {
        codeUrl.value = "data:image/gif;base64," + res.img
        loginForm.value.uuid = res.uuid
      }
    })
  }

  function getCookie() {
    const username = Cookies.get("username")
    const password = Cookies.get("password")
    const rememberMe = Cookies.get("rememberMe")
    loginForm.value = {
      username: username === undefined ? loginForm.value.username : username,
      password: password === undefined ? loginForm.value.password : decrypt(password),
      rememberMe: rememberMe === undefined ? false : Boolean(rememberMe)
    }
  }

  getCode()
  getCookie()
</script>
