<script setup lang="ts">
  import { ref, reactive } from "vue"
  import { ElMessage } from 'element-plus'

  const tableColumnHeader = [
    { label: "模型名称", prop: "modelName", align: "center" },
    { label: "变更类型", prop: "changeType", align: "center" },
    { label: "变更描述", prop: "changeDesc", align: "center" },
    { label: "申请人", prop: "applicant", align: "center" },
    { label: "申请时间", prop: "applyTime", align: "center" },
    { label: "审核人", prop: "auditor", align: "center" },
    { label: "审核状态", prop: "auditStatus", align: "center" },
    { label: "审核时间", prop: "auditTime", align: "center" },
    { label: "审核意见", prop: "auditComment", align: "center" },
  ]

  const state = reactive({
    tableTotal: 0,
    tableList: [
      {
        modelName: '图像识别模型',
        changeType: '参数调整',
        changeDesc: '优化识别阈值参数',
        applicant: '张三',
        applyTime: '2023-07-01 09:00:00',
        auditor: '李四',
        auditStatus: '待审核',
        auditTime: '',
        auditComment: ''
      },
      {
        modelName: '语音识别模型',
        changeType: '模型升级',
        changeDesc: '升级至v2.0，提升准确率',
        applicant: '王五',
        applyTime: '2023-07-02 10:30:00',
        auditor: '赵六',
        auditStatus: '已拒绝',
        auditTime: '2023-07-03 14:00:00',
        auditComment: '请补充升级说明'
      }
      // ...可添加更多示例...
    ],
    params: {
      page: 1,
      pageSize: 10
    }
  })
  // 表格操作
  const tableChangeSelect = (val: any) => {
    console.log(val)
  }
  // 表格删除
  const tableDelete = (row: any) => {
    ElMessage.success('删除成功')
  }
  // 表格编辑
  const tableEdit = (row: any) => {
    console.log(row)
  }
  function handleFilter({ type, data }) {
    console.log('筛选条件:', data)
    // 调用查询接口
  }


</script>
<template>
  <section class="mode_list p-5">
    <!-- <TableFilter :filterList="filters" :clearable="true" @submit="handleFilter" /> -->
    <!-- 表格 -->
    <TableColumn :data="state.tableList" :total="state.tableTotal" :column="tableColumnHeader"
      :page.sync="state.params.page" :pageSize.sync="state.params.pageSize" @changeSelect="tableChangeSelect">
      <template #setting="{ row }">
        <el-button @click="tableDelete(row)" type="primary" link size="small">删除</el-button>
        <el-button @click="tableEdit(row)" type="primary" link size="small">编辑</el-button>
      </template>
    </TableColumn>

  </section>
</template>
<style lang="scss" scoped></style>
