<script setup lang="ts">
  import { ref, reactive } from "vue"
  import { ElMessage } from 'element-plus'

  const tableColumnHeader = [
    { label: "角色名", prop: "roleName", align: "center" },
    { label: "用户名", prop: "userName", align: "center" },
    { label: "模型/数据名称", prop: "resourceName", align: "center" },
    { label: "权限类型", prop: "permissionType", align: "center" },
    { label: "权限状态", prop: "permissionStatus", align: "center" },
    { label: "分配时间", prop: "assignTime", align: "center" },
    { label: "审批状态", prop: "approvalStatus", align: "center" },
    { label: "冲突检测", prop: "conflict", align: "center" },
  ]

  const state = reactive({
    tableTotal: 0,
    tableList: [
      {
        roleName: '管理员',
        userName: '张三',
        resourceName: '图像识别模型',
        permissionType: '读/写/删除',
        permissionStatus: '已分配',
        assignTime: '2023-06-01 10:00:00',
        approvalStatus: '已通过',
        conflict: '无'
      },
      {
        roleName: '普通用户',
        userName: '李四',
        resourceName: '数据集A',
        permissionType: '读',
        permissionStatus: '待审批',
        assignTime: '2023-06-02 11:20:00',
        approvalStatus: '待审批',
        conflict: '存在冲突'
      }
      // ...可添加更多示例...
    ],
    params: {
      page: 1,
      pageSize: 10
    }
  })
  // 表格操作
  const tableChangeSelect = (val: any) => {
    console.log(val)
  }
  // 表格删除
  const tableDelete = (row: any) => {
    ElMessage.success('删除成功')
  }
  // 表格编辑
  const tableEdit = (row: any) => {
    console.log(row)
  }
  function handleFilter({ type, data }) {
    console.log('筛选条件:', data)
    // 调用查询接口
  }


</script>
<template>
  <section class="mode_list p-5">
    <!-- <TableFilter :filterList="filters" :clearable="true" @submit="handleFilter" /> -->
    <!-- 表格 -->
    <TableColumn :data="state.tableList" :total="state.tableTotal" :column="tableColumnHeader"
      :page.sync="state.params.page" :pageSize.sync="state.params.pageSize" @changeSelect="tableChangeSelect">
      <template #setting="{ row }">
        <el-button @click="tableDelete(row)" type="primary" link size="small">删除</el-button>
        <el-button @click="tableEdit(row)" type="primary" link size="small">编辑</el-button>
      </template>
    </TableColumn>

  </section>
</template>
<style lang="scss" scoped></style>
