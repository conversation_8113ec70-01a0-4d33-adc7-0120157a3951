<template>
	<div class="system-role-dialog-container">
		<el-dialog :title="state.dialog.title" v-model="state.dialog.isShowDialog" width="769px">
			<el-form ref="roleDialogFormRef" :model="state.ruleForm" size="default" label-width="90px">
				<el-row :gutter="35">
					<el-col :xs="24" :sm="12" :md="12" :lg="12" :xl="12" class="mb20">
						<el-form-item label="模型文件">
							<el-upload ref="upload" class="upload-demo"
								action="https://run.mocky.io/v3/9d059bf9-4660-45f2-925d-ce80ad6c4d15" :limit="1"
								:auto-upload="false">
								<el-button class="ml-3" type="primary" size="default">
									上传
								</el-button>
								<template #tip>
									<div class="el-upload__tip text-red">
									</div>
								</template>
							</el-upload>
						</el-form-item>
					</el-col>
					<el-col :xs="24" :sm="12" :md="12" :lg="12" :xl="12" class="mb20">
						<el-form-item label="模型名称">
							<el-input v-model="state.ruleForm.m_name" placeholder="请输入模型名称" clearable></el-input>
						</el-form-item>
					</el-col>
					<el-col :xs="24" :sm="12" :md="12" :lg="12" :xl="12" class="mb20">
						<el-form-item label="模型版本">
							<el-input v-model="state.ruleForm.version" placeholder="请输入模型版本" clearable></el-input>
						</el-form-item>
					</el-col>
					<el-col :xs="24" :sm="12" :md="12" :lg="12" :xl="12" class="mb20">
						<el-form-item label="模型分类">
							<!-- 文本 图片 多模态 -->
							<el-select v-model="state.ruleForm.m_type" placeholder="请选择模型分类">
								<el-option label="文本" value="text"></el-option>
								<el-option label="图片" value="image"></el-option>
								<el-option label="多模态" value="multimodal"></el-option>
							</el-select>
						</el-form-item>
					</el-col>
					<el-col :xs="12" :sm="12" :md="12" :lg="12" :xl="12" class="mb20">
						<!-- 是否有做成自定义上传的需求 -->
						<el-form-item label="模型图标">
							<!-- <el-select v-model="state.ruleForm.status" placeholder="请选择模型图标">
								<el-option label="启用" value="1"></el-option>
								<el-option label="禁用" value="0"></el-option>
							</el-select> -->
							<!-- <vue-initials-img name="Frank Underwood"/> -->
							<Avatar :name="state.ruleForm.m_name || '模型'" :size="20" background="#42b883" color="#fff"
								:rounded="true" />
						</el-form-item>
					</el-col>
					<el-col :xs="12" :sm="12" :md="12" :lg="12" :xl="12" class="mb20">
						<el-form-item label="模型状态">
							<el-select v-model="state.ruleForm.status" placeholder="请选择模型状态">
								<el-option label="启用" value="1"></el-option>
								<el-option label="禁用" value="0"></el-option>
							</el-select>
						</el-form-item>
					</el-col>
					<el-col :xs="12" :sm="12" :md="12" :lg="12" :xl="12" class="mb20">
						<el-form-item label="模型描述">
							<el-input v-model="state.ruleForm.describe" type="textarea" placeholder="请输入模型描述"
								maxlength="150"></el-input>
						</el-form-item>
					</el-col>
				</el-row>
			</el-form>
			<template #footer>
				<span class="dialog-footer">
					<el-button @click="onCancel" size="default">
						取 消
					</el-button>
					<el-button type="primary" @click="onSubmit" size="default">
						{{ state.dialog.submitTxt }}
					</el-button>
				</span>
			</template>
		</el-dialog>
	</div>
</template>

<script setup lang="ts" name="systemRoleDialog">
	import { reactive, ref } from 'vue';
	import Avatar from './Avatar.vue';

	// 定义子组件向父组件传值/事件
	const emit = defineEmits(['refresh']);

	// 定义变量内容
	const roleDialogFormRef = ref();
	const state = reactive({
		ruleForm: {
			m_name: '', // 模型名称
			m_type: '', // 模型标识
			version: '', // 模型标识
			sort: 0, // 排序
			status: '1', // 模型状态
			describe: '', // 模型描述
		},
		menuProps: {
			children: 'children',
			label: 'label',
		},
		dialog: {
			isShowDialog: false,
			type: '',
			title: '',
			submitTxt: '',
		},
	});

	// 打开弹窗
	const openDialog = (type: string, row) => {
		if (type === 'edit') {
			state.ruleForm = row;
			state.dialog.title = '修改模型';
			state.dialog.submitTxt = '修 改';
		} else {
			state.ruleForm = {
				m_name: '', // 模型名称
				m_type: '', // 模型标识
				version: '', // 模型标识
				sort: 0, // 排序
				status: '1', // 模型状态
				describe: '', // 模型描述
			};
			state.dialog.title = '新增模型';
			state.dialog.submitTxt = '新 增';
			// 清空表单，此项需加表单验证才能使用
			// nextTick(() => {
			// 	roleDialogFormRef.value.resetFields();
			// });
		}
		state.dialog.isShowDialog = true;
	};
	// 关闭弹窗
	const closeDialog = () => {
		state.dialog.isShowDialog = false;
	};
	// 取消
	const onCancel = () => {
		closeDialog();
	};
	// 提交
	const onSubmit = () => {
		closeDialog();
		emit('refresh');
		// if (state.dialog.type === 'add') { }
	};
	// 暴露变量
	defineExpose({
		openDialog,
	});
</script>

<style scoped lang="scss">
	.system-role-dialog-container {
		.menu-data-tree {
			width: 100%;
			border: 1px solid var(--el-border-color);
			border-radius: var(--el-input-border-radius, var(--el-border-radius-base));
			padding: 5px;
		}
	}
</style>
