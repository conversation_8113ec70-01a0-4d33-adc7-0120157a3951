<template>
  <div class="table_column">
    <el-table :data="data" @selection-change="handleSelectionChange">
      <el-table-column v-if="showSelection" type="selection" />

      <el-table-column
        v-if="showIndex"
        type="index"
        label="序号"
        width="80"
        :index="1"
      />

      <!-- 处理后的多级表头 -->
      <template v-for="(item, index) in processedColumns" :key="item.label + index">
        <el-table-column
          v-if="item.children && item.children.length"
          :label="item.label"
          :width="item.width"
          :align="item.align || 'center'"
        >
          <!-- 顶级表头居中对齐 -->

          <!-- 子表头渲染 -->
          <template v-for="(child, childIndex) in item.children" :key="child.label + childIndex">
            <el-table-column
              v-if="child.children && child.children.length"
              :label="child.label"
              :width="child.width"
              :align="child.align || 'left'"
            >
              <!-- 二级表头默认左对齐 -->

              <!-- 孙表头渲染 -->
              <template v-for="(grandChild, grandChildIndex) in child.children" :key="grandChild.label + grandChildIndex">
                <el-table-column
                  v-if="grandChild.slot"
                  :label="grandChild.label"
                  :width="grandChild.width"
                  :align="grandChild.align || 'left'"
                >
                  <template #default="scope">
                    <slot :name="grandChild.prop" v-bind="scope" />
                  </template>
                </el-table-column>
                <el-table-column
                  v-else
                  :label="grandChild.label"
                  :prop="grandChild.prop"
                  :width="grandChild.width"
                  :align="grandChild.align || 'left'"
                />
              </template>
            </el-table-column>

            <el-table-column
              v-else-if="child.slot"
              :label="child.label"
              :width="child.width"
              :align="child.align || 'left'"
            >
              <template #default="scope">
                <slot :name="child.prop" v-bind="scope" />
              </template>
            </el-table-column>

            <el-table-column
              v-else
              :label="child.label"
              :prop="child.prop"
              :width="child.width"
              :align="child.align || 'left'"
            />
          </template>
        </el-table-column>

        <el-table-column
          v-else-if="item.slot"
          :label="item.label"
          :width="item.width"
          :align="item.align || 'left'"
        >
          <template #default="scope">
            <slot :name="item.prop" v-bind="scope" />
          </template>
        </el-table-column>

        <el-table-column
          v-else
          :label="item.label"
          :prop="item.prop"
          :width="item.width"
          :align="item.align || 'left'"
        >
          <template v-if="item.slot" #default="scope">
            <slot :name="item.prop" v-bind="scope" />
          </template>
        </el-table-column>
      </template>

      <el-table-column
        v-if="showSetting"
        fixed="right"
        label="操作"
        width="150"
      >
        <template #default="scope">
          <slot name="setting" v-bind="scope" />
        </template>
      </el-table-column>
    </el-table>

    <div class="flex justify-end mt20">
      <el-pagination
        @current-change="page => changePageInfo(page, 'page')"
        @size-change="pageSize => changePageInfo(pageSize, 'pageSize')"
        :current-page="page"
        :page-size="pageSize"
        :page-sizes="[100, 200, 300, 400]"
        layout="total, sizes, prev, pager, next, jumper"
        :total="total"
      />
    </div>
  </div>
</template>

<script setup>
import { computed, toRefs } from 'vue';

const props = defineProps({
  data: {
    type: Array,
    default: () => [],
  },
  column: {
    type: Array,
    default: () => [],
  },
  total: {
    type: Number,
    default: 0,
  },
  showSelection: {
    type: Boolean,
    default: true,
  },
  showIndex: {
    type: Boolean,
    default: true,
  },
  showSetting: {
    type: Boolean,
    default: true,
  },
  page: {
    type: Number,
    default: 1,
  },
  pageSize: {
    type: Number,
    default: 100,
  },
});

const emit = defineEmits(['changeSelect', 'update:page', 'update:pageSize', 'onChange']);

const { column } = toRefs(props);

const processedColumns = computed(() => {
  return column.value.map((col) => {
    if (col.children && col.children.length) {
      const totalWidth = col.children.reduce((width, child) => {
        if (child.children && child.children.length) {
          return (
            width +
            child.children.reduce(
              (w, grandChild) => w + (parseInt(grandChild.width) || 0),
              0
            )
          );
        }
        return width + (parseInt(child.width) || 0);
      }, 0);

      return {
        ...col,
        width: col.width || totalWidth.toString(),
      };
    }
    return col;
  });
});

function handleSelectionChange(selection) {
  const ids = selection.map(item => item.id);
  emit('changeSelect', { data: selection, dataIds: ids });
}

function changePageInfo(val, type) {
  emit(`update:${type}`, val);
  emit('onChange');
}
</script>

<style scoped lang="scss">
.table_column {
  width: 100%;
  height: 100%;
}

/* 确保顶级表头文字居中 */
.el-table__header th.gutter {
  display: table-cell !important;
}

/* 可以根据需要调整表头样式 */
:deep(.el-table__header th) {
  background-color: #f5f7fa;
  font-weight: bold;
}
</style>
