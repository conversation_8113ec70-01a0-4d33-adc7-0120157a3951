<script setup lang="ts">
  import { ref, reactive } from "vue"
  import { ElMessage } from 'element-plus'
  const filters = [
    {
      type: 'input',
      label: '用户名',
      attr: 'username',
      placeholder: '请输入用户名',
      clearable: true
    },
    {
      type: 'select',
      label: '角色',
      attr: 'role',
      placeholder: '请选择角色',
      clearable: true,
      options: [
        { name: '管理员', key: 'admin' },
        { name: '普通用户', key: 'user' }
      ]
    },
    {
      type: 'datetimerange',
      label: '注册时间',
      attr: 'registerTime',
      startAttr: 'startTime',
      endAttr: 'endTime',
      placeholder: '请选择注册时间',
      startPlaceholder: '开始时间',
      endPlaceholder: '结束时间',
      // format: 'yyyy-MM-dd HH:mm:ss',
      clearable: true
    },
    {
      type: 'input',
      label: '模型名称',
      attr: 'modelName',
      placeholder: '请输入模型名称',
      clearable: true
    },
    {
      type: 'input',
      label: '版本号',
      attr: 'version',
      placeholder: '请输入版本号',
      clearable: true
    },
    {
      type: 'datetimerange',
      label: '更新时间',
      attr: 'updateTime',
      startAttr: 'updateStartTime',
      endAttr: 'updateEndTime',
      placeholder: '请选择更新时间',
      startPlaceholder: '开始时间',
      endPlaceholder: '结束时间',
      clearable: true
    }
  ]

  const tableColumnHeader = [
    // 版本号 创建时间  描述 模型大小 模型类型 依赖环境 模型ID
    { label: "模型名称", prop: "modelName", align: "center", },
    { label: "版本号", prop: "version", width: "120px", align: "center", },
    { label: "创建时间", prop: "createTime", align: "center", },
    { label: "上传人", prop: "uploader", align: "center", },
    { label: "描述", prop: "description", align: "center", },
    { label: "模型大小", prop: "modelSize", align: "center", },
    { label: "版本状态", prop: "status", align: "center", },
    { label: "审批流程记录", prop: "approvalRecords", align: "center", },
  ]

  const state = reactive({
    tableTotal: 0,
    tableList: [
      {
        modelName: '图像识别模型',
        version: '1.0.0',
        createTime: '2023-04-01 12:00:00',
        uploader: '张三',
        description: '初始版本，支持基础图像识别',
        modelSize: '100MB',
        status: '部署上线',
        approvalRecords: '李四:同意; 王五:同意'
      },
      {
        modelName: '图像识别模型',
        version: '1.1.0',
        createTime: '2023-05-10 09:30:00',
        uploader: '李四',
        description: '优化识别速度',
        modelSize: '110MB',
        status: '下线',
        approvalRecords: '王五:同意; 赵六:不同意'
      }
      // ...可添加更多示例...
    ],
    params: {
      page: 1,
      pageSize: 10
    }
  })
  // 表格操作
  const tableChangeSelect = (val: any) => {
    console.log(val)
  }
  // 表格删除
  const tableDelete = (row: any) => {
    ElMessage.success('删除成功')
  }
  // 表格编辑
  const tableEdit = (row: any) => {
    console.log(row)
  }
  function handleFilter({ type, data }) {
    console.log('筛选条件:', data)
    // 调用查询接口
  }


</script>
<template>
  <section class="mode_list p-5">
    <!-- <TableFilter :filterList="filters" :clearable="true" @submit="handleFilter" /> -->
    <!-- 表格 -->
    <TableColumn :data="state.tableList" :total="state.tableTotal" :column="tableColumnHeader"
      :page.sync="state.params.page" :pageSize.sync="state.params.pageSize" @changeSelect="tableChangeSelect">
      <template #setting="{ row }">
        <el-button @click="tableDelete(row)" type="primary" link size="small">删除</el-button>
        <el-button @click="tableEdit(row)" type="primary" link size="small">编辑</el-button>
      </template>
    </TableColumn>

  </section>
</template>
<style lang="scss" scoped></style>
