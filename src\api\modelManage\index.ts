import service from "@/utils/request";

// 模型列表查询
export function getModelList(params) {
  return service({
    baseURL: '/',
    url: "/mock/models/import.json",
    // url: "/models/get",
    method: "get",
    params,
  });
}

// 模型导入
export function importModel(data) {
  return service({
    url: "/models/create",
    method: "POST",
    data
  });
}

// form-data 方式 待测试
export function importModel2({ file, ...params }) {
  const formData = new FormData();
  formData.append('file', file); // 文件

  // 其他参数依次添加
  for (const key in params) {
    formData.append(key, params[key]);
  }

  return service({
    url: "/models/create",
    method: "POST",
    data: formData,
    headers: {
      'Content-Type': 'multipart/form-data'
    }
  });
}


// 模型删除
export function delModel1(mid) {
  return service({
    url: "/models/soft_delete/"+ mid,
    method: "delete",
  });
}

// 模型删除
export function delModel2(mid) {
  return service({
    url: "/models/hard_delete/"+ mid,
    method: "delete",
  });
}

// 模型编辑更新
export function updateModel1(mid) {
  return service({
    url: "/models/update/"+ mid,
    method: "patch",
  });
}

// 具体接口待定