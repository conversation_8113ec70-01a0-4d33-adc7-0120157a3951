<!-- InitialsAvatar.vue -->
<template>
  <canvas
    ref="canvas"
    :width="size"
    :height="size"
    :style="canvasStyle"
  ></canvas>
</template>

<script setup>
import { onMounted, ref, watch, computed } from 'vue';

const props = defineProps({
  name: String,
  size: { type: Number, default: 64 },
  background: { type: String, default: '#007bff' },
  color: { type: String, default: '#fff' },
  rounded: { type: Boolean, default: true },
});

const canvas = ref();

const canvasStyle = computed(() => ({
  width: `${props.size}px`,
  height: `${props.size}px`,
  borderRadius: props.rounded ? '50%' : '0',
  overflow: 'hidden',
  display: 'block',
}));

const draw = () => {
  const ctx = canvas.value.getContext('2d');
  ctx.fillStyle = props.background;
  ctx.fillRect(0, 0, props.size, props.size);

  const initials = props.name
    .split(' ')
    .map(word => word[0]?.toUpperCase())
    .slice(0, 2)
    .join('');

  ctx.fillStyle = props.color;
  ctx.font = `${props.size * 0.5}px sans-serif`;
  ctx.textAlign = 'center';
  ctx.textBaseline = 'middle';
  ctx.fillText(initials, props.size / 2, props.size / 2);
};

onMounted(draw);
watch(() => props.name, draw);
</script>
