<script setup lang="ts">
import {ref,reactive} from "vue"
import {ElMessage} from 'element-plus'
// import TableColumn from "@/components/by-list/table-column.vue"
const tableColumnHeader = [
  // 版本号 创建时间  描述 模型大小 模型类型 依赖环境 模型ID
  {
    label: "版本号",
    prop: "version",
    width: "120px",
    align: "center",
  },
  {
    label: "创建时间",
    prop: "createTime",
    align: "center",
  },
  {
    label: "描述",
    prop: "description",
    align: "center",
  },
  {
    label: "模型大小",
    prop: "modelSize",
    align: "center",
  },
]

const state= reactive({
  tableTotal:0,
  tableList:[
    {
      version:'1.0.0',
      createTime:'2023-04-01 12:00:00',
      description:'模型描述',
      modelSize:'100MB'

    }
  ],
  params:{
    page:1,
    pageSize:10
  }


})
// 表格操作
const tableChangeSelect = (val: any) => {
  console.log(val)
}
// 表格删除
const tableDelete = (row: any) => {
  ElMessage.success('删除成功')
}
// 表格编辑
const tableEdit = (row: any) => {
  console.log(row)
}


</script>
<template>
  <section class="mode_list">

    <!-- 表格 -->
    <TableColumn
      :data="state.tableList"
      :total="state.tableTotal"
      :column="tableColumnHeader"
      :page.sync="state.params.page"
      :pageSize.sync="state.params.pageSize"
      @changeSelect="tableChangeSelect"
    >
      <template #setting="{ row }">
        <el-button @click="tableDelete(row)" type="text" size="small"
          >删除</el-button
        >
        <el-button @click="tableEdit(row)" type="text" size="small"
          >编辑</el-button
        >
      </template>
    </TableColumn>

  </section>
</template>
<style lang="scss" scoped>
</style>
