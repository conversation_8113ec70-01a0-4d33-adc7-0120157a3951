<!-- 模型历史记录 -->
<template>
  <div class="system-role-container w100 h100">
    <div class="system-role-padding layout-padding-auto layout-padding-view">
      <div class="system-user-search mb15" style="display: flex; justify-content: space-between;">
        <el-form ref="queryFormRef" :model="state.tableData.param" inline>
          <el-form-item prop="search">
            <el-input v-model="state.tableData.param.search" size="default" placeholder="请输入模型名称"
              style="max-width: 180px" clearable @keyup.enter="getTableData" />
          </el-form-item>
          <el-form-item prop="type">
            <el-select v-model="state.tableData.param.type" placeholder="模型类别" style="width: 140px" clearable>
              <el-option v-for="item in modelTypeOptions" :key="item.value" :label="item.label" :value="item.value" />
            </el-select>
          </el-form-item>
          <el-form-item prop="status">
            <el-select v-model="state.tableData.param.status" placeholder="模型状态" style="width: 120px" clearable>
              <el-option v-for="item in statusOptions" :key="item.label" :label="item.label" :value="item.value" />
            </el-select>
          </el-form-item>
          <el-form-item prop="dateRange">
            <el-date-picker v-model="state.tableData.param.dateRange" type="daterange" range-separator="-"
              start-placeholder="开始日期" end-placeholder="结束日期" style="max-width: 240px" value-format="YYYY-MM-DD" />
          </el-form-item>
          <el-form-item>
            <el-button type="primary" @click="getTableData">
              <el-icon><ele-Search /></el-icon>
              查询
            </el-button>
            <el-button @click="onResetQuery">
              <el-icon>
                <Refresh />
              </el-icon>
              重置
            </el-button>
          </el-form-item>
        </el-form>
        <div class="action-buttons">
          <el-button size="default" type="success" class="ml10" @click="onOpenAddRole('add')">
            <el-icon>
              <ele-FolderAdd />
            </el-icon>
            模型导入
          </el-button>
          <el-button size="default" type="danger" class="ml10" @click="onBatchDelete"
            :disabled="selectedRows.length === 0">
            <el-icon>
              <Delete />
            </el-icon>
            批量删除
          </el-button>
        </div>
      </div>
      <el-table :data="state.tableData.data" v-loading="state.tableData.loading" class="table_list" style="width: 100%"
        @selection-change="onSelectionChange" @sort-change="onSortChange">
        <el-table-column type="selection" width="55" />
        <el-table-column type="index" label="序号" width="60" />
        <el-table-column prop="m_name" label="模型名称" show-overflow-tooltip></el-table-column>
        <el-table-column prop="version" label="模型版本" width="90" show-overflow-tooltip></el-table-column>
        <el-table-column prop="m_type" label="模型类别" width="90" show-overflow-tooltip></el-table-column>
        <el-table-column prop="m_size" label="模型大小" width="90" show-overflow-tooltip>
        </el-table-column>
        <el-table-column prop="m_status" label="模型状态" show-overflow-tooltip>
          <template #default="scope">
            <el-tag disable-transitions type="success" v-if="scope.row.status">启用</el-tag>
            <el-tag type="info" v-else disable-transitions>禁用</el-tag>
          </template>
        </el-table-column>
        <el-table-column prop="describe" label="模型描述" show-overflow-tooltip></el-table-column>
        <el-table-column prop="createTime" label="创建时间" show-overflow-tooltip sortable="custom"></el-table-column>
        <el-table-column label="操作" width="240">
          <template #default="scope">
            <el-button size="small" text type="primary" @click="onOpenEdit('edit', scope.row)">
              <el-icon>
                <Edit />
              </el-icon>
              修改
            </el-button>
            <el-button size="small" text type="danger" @click="onRowDel(scope.row)">
              <el-icon>
                <Delete />
              </el-icon> 删除
            </el-button>
            <el-dropdown size="small" @command="(command) => handleCommand(command, scope.row)" class="ml10">
              <el-button size="small" text type="primary">
                更多<el-icon class="el-icon--right">
                  <ArrowDown />
                </el-icon>
              </el-button>
              <template #dropdown>
                <el-dropdown-menu>
                  <el-dropdown-item command="history" :icon="Clock">历史记录</el-dropdown-item>
                  <!-- <el-dropdown-item command="myApproval" :icon="Stamp">我的审批</el-dropdown-item> -->
                  <el-dropdown-item command="approvalHistory" :icon="Finished">审批记录</el-dropdown-item>
                </el-dropdown-menu>
              </template>
            </el-dropdown>
          </template>
        </el-table-column>
      </el-table>
      <el-pagination @size-change="onHandleSizeChange" @current-change="onHandleCurrentChange" class="mt15"
        :pager-count="5" :page-sizes="[10, 20, 30]" v-model:current-page="state.tableData.param.pageNum" background
        v-model:page-size="state.tableData.param.pageSize" layout="total, sizes, prev, pager, next, jumper"
        :total="state.tableData.total">
      </el-pagination>
    </div>
    <RoleDialog ref="roleDialogRef" @refresh="getTableData()" />
  </div>
</template>

<script setup lang="ts" name="systemRole">
  import { defineAsyncComponent, reactive, onMounted, ref } from "vue";
  import { Refresh, ArrowDown, Clock, Stamp, Finished } from '@element-plus/icons-vue';
  import { ElMessageBox, ElMessage } from "element-plus";
  import { getModelList } from "@/api/modelManage";

  // 引入组件
  const RoleDialog = defineAsyncComponent(
    () => import("./components/dialogImport.vue")
  );

  // 定义变量内容
  const roleDialogRef = ref();
  const queryFormRef = ref();
  const state = reactive({
    tableData: {
      data: [] as IImportModel[],
      total: 0,
      loading: false,
      param: {
        search: "",
        type: "",
        status: null as boolean | null,
        sortProp: "",
        sortOrder: "",
        dateRange: [] as any[],
        pageNum: 1,
        pageSize: 10,
      },
    },
  });
  // 已选中的行
  const selectedRows = ref<IImportModel[]>([]);
  const onSelectionChange = (rows: IImportModel[]) => {
    selectedRows.value = rows;
  };
  const onBatchDelete = () => {
    if (selectedRows.value.length === 0) {
      ElMessage.warning("请先选择要删除的模型");
      return;
    }
    ElMessageBox.confirm(
      `确定要删除选中的 ${selectedRows.value.length} 个模型吗？`,
      "提示",
      {
        confirmButtonText: "确认",
        cancelButtonText: "取消",
        type: "warning",
      }
    ).then(() => {
      state.tableData.data = state.tableData.data.filter(
        (item) => !selectedRows.value.includes(item)
      );
      state.tableData.total = state.tableData.data.length;
      selectedRows.value = [];
      ElMessage.success("删除成功");
    });
  };
  // 选项数据
  const modelTypeOptions = [
    { label: "分类", value: "分类" },
    { label: "回归", value: "回归" },
    { label: "聚类", value: "聚类" },
  ];
  const statusOptions = [
    { label: "启用", value: true },
    { label: "禁用", value: false },
  ];
  // 排序变化
  const onSortChange = (sort: { prop: string; order: string }) => {
    state.tableData.param.sortProp = sort.prop;
    state.tableData.param.sortOrder = sort.order;
    // Element Plus order: ascending/descending/null
    const orderFactor = sort.order === "ascending" ? 1 : -1;
    if (sort.order) {
      state.tableData.data.sort((a: any, b: any) => {
        if (a[sort.prop] === b[sort.prop]) return 0;
        return a[sort.prop] > b[sort.prop] ? orderFactor : -orderFactor;
      });
    }
  };
  // 初始化表格数据
  const getTableData = () => {
    state.tableData.loading = true;
    getModelList({}).then((res) => {
      let list: IImportModel[] = res.data;
      state.tableData.data = list;
      state.tableData.total = list.length;
      state.tableData.loading = false;
    });
  };

  // 打开新增模型弹窗
  const onOpenAddRole = (type: string) => {
    roleDialogRef.value.openDialog(type);
  };
  // 打开修改模型弹窗
  const onOpenEdit = (type: string, row: Object) => {
    roleDialogRef.value.openDialog(type, row);
  };
  // 删除模型
  const onRowDel = (row: IImportModel) => {
    ElMessageBox.confirm(
      `此操作将永久删除模型名称：“${row.m_name}”，是否继续?`,
      "提示",
      {
        confirmButtonText: "确认",
        cancelButtonText: "取消",
        type: "warning",
      }
    )
      .then(() => {
        getTableData();
        ElMessage.success("删除成功");
      })
      .catch(() => { });
  };
  // 分页改变
  const onHandleSizeChange = (val: number) => {
    state.tableData.param.pageSize = val;
    getTableData();
  };
  // 分页改变
  const onHandleCurrentChange = (val: number) => {
    state.tableData.param.pageNum = val;
    getTableData();
  };
  // 表格操作下拉菜单
  const handleCommand = (command: string, row: IImportModel) => {
    if (command === 'history') {
      onOpenEdit('history', row);
    } else if (command === 'myApproval') {
      // handle my approval
      console.log('myApproval', row);
      ElMessage.info(`处理【${row.m_name}】的我的审批`);
    } else if (command === 'approvalHistory') {
      // handle approval history
      console.log('approvalHistory', row);
      ElMessage.info(`查看【${row.m_name}】的审批历史`);
    }
  };

  // 重置查询
  const onResetQuery = () => {
    if (!queryFormRef.value) return;
    queryFormRef.value.resetFields();
    getTableData();
  };

  // 页面加载时
  onMounted(() => {
    getTableData();
  });
</script>

<style scoped lang="scss">
  .system-user-search {
    width: 100%;

    .el-form-item {
      margin-bottom: 0;
    }

    .action-buttons {
      white-space: nowrap;
    }

    .table_list {
      height: calc(100vh - 250px);
    }
  }
</style>
