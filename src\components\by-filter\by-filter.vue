<template>
  <el-form :inline="true" :model="form" :class="{ 'no-margin': noMargin }">
    <el-form-item v-for="(item, index) in filterList" :key="index" :label="item.label">
      <el-input v-if="item.type === 'input'" v-model="form[item.attr]" :placeholder="item.placeholder"
        :clearable="item.clearable !== undefined ? item.clearable : clearable" @keyup.enter="submit" />
      <el-select v-else-if="item.type === 'select'" v-model="form[item.attr]" :placeholder="item.placeholder"
        :clearable="item.clearable !== undefined ? item.clearable : clearable">
        <el-option v-for="(option, key) in item.options" :key="key" :label="option.name" :value="option.key" />
      </el-select>
      <el-date-picker v-else-if="showDatePicker(item.type)" v-model="form[item.attr]" :type="item.type"
        :placeholder="item.placeholder" :range-separator="item.rangeSeparator || '至'"
        :end-placeholder="item.endPlaceholder" :start-placeholder="item.startPlaceholder"
        :format="item.format || format" :value-format="item.format || format"
        @change="onDateChange(item.startAttr, item.endAttr, form[item.attr])" />
    </el-form-item>
    <el-form-item>
      <el-button type="primary" @click="submit">查询</el-button>
      <el-button type="" @click="submit">重置</el-button>
    </el-form-item>
  </el-form>
</template>

<script setup>
  import { ref, computed, watchEffect } from 'vue'

  const props = defineProps({
    noMargin: {
      type: Boolean,
      default: false
    },
    filterList: {
      type: Array,
      default: () => []
    },
    clearable: {
      type: Boolean,
      default: true
    },
    format: {
      type: String,
      default: 'yyyy-MM-dd'
    }
  })

  const emit = defineEmits(['submit'])

  const targetTypes = ['datetimerange', 'daterange', 'monthrange']
  const form = ref({})
  const hasRange = ref(false)

  const showDatePicker = (type) =>
    'year/month/date/dates/months/years/week/datetime/datetimerange/daterange/monthrange'.includes(type)

  function initForm() {
    props.filterList.forEach(item => {
      form.value[item.attr] = item.defaultValue || item.defaultValue === 0 ? item.defaultValue : ''
      if (item.defaultValue) {
        if (item.attr === 'sendDate' || item.attr === 'timeRange') {
          onDateChange(item.startAttr, item.endAttr, form.value[item.attr])
        }
      }
    })
    let filterTypes = props.filterList.map(item => item.type)
    hasRange.value = filterTypes.some(item => targetTypes.includes(item))
  }

  function onDateChange(start, end, value) {
    if (value) {
      if (hasRange.value && value.length) {
        form.value[start] = value[0]
        form.value[end] = value[1]
        return
      }
      let monthArr = value.split('-')
      form.value[start] = monthArr[0]
      form.value[end] = monthArr[1]
      return
    }
    form.value[start] = form.value[end] = ''
  }

  function submit() {
    let submitData = { ...form.value }
    if (hasRange.value) {
      props.filterList.forEach(item => {
        if (targetTypes.includes(item.type)) delete submitData[item.attr]
      })
    }
    if (submitData.sendDate) delete submitData.sendDate
    emit('submit', {
      type: 'filter',
      data: submitData
    })
  }

  watchEffect(() => {
    initForm()
  })
</script>

<style scoped lang="scss">
  :deep(.no-margin) {
    .el-form-item {
      margin-bottom: 0;
    }
  }
</style>
