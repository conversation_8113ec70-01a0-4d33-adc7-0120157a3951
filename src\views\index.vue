<template>
  <div class="home-new px-4 py-8 bg-gray-50 dark:bg-gray-900">
    <!-- 顶部统计卡片区 -->
    <div class="grid grid-cols-1 sm:grid-cols-2 xl:grid-cols-4 gap-6 mb-8">
      <!-- 总模型数 -->
      <div class="relative overflow-hidden rounded-2xl border border-blue-200 dark:border-blue-700 bg-white dark:bg-gray-800 p-6 shadow-sm">
        <div class="text-sm text-blue-900 dark:text-blue-100 font-medium">总模型数</div>
        <div class="text-4xl font-extrabold text-blue-950 dark:text-white my-2">24</div>
        <div class="text-sm text-blue-600 dark:text-blue-300">+2 较上周</div>
        <div class="absolute right-4 top-4 text-blue-700 dark:text-blue-200 text-2xl">📊</div>
      </div>

      <!-- 运行中 -->
      <div class="relative overflow-hidden rounded-2xl border border-green-200 dark:border-green-700 bg-white dark:bg-gray-800 p-6 shadow-sm">
        <div class="text-sm text-green-900 dark:text-green-100 font-medium">运行中</div>
        <div class="text-4xl font-extrabold text-green-950 dark:text-white my-2">18</div>
        <div class="text-sm text-green-600 dark:text-green-300">+1 较上周</div>
        <div class="absolute right-4 top-4 text-green-700 dark:text-green-200 text-2xl">🚀</div>
      </div>

      <!-- 健康状态 -->
      <div class="relative overflow-hidden rounded-2xl border border-purple-200 dark:border-purple-700 bg-white dark:bg-gray-800 p-6 shadow-sm">
        <div class="text-sm text-purple-900 dark:text-purple-100 font-medium">健康状态</div>
        <div class="text-4xl font-extrabold text-purple-950 dark:text-white my-2">16</div>
        <div class="text-sm text-red-500">-1 较上周</div>
        <div class="absolute right-4 top-4 text-purple-700 dark:text-purple-200 text-2xl">💖</div>
      </div>

      <!-- 需警关注 -->
      <div class="relative overflow-hidden rounded-2xl border border-yellow-300 dark:border-yellow-700 bg-white dark:bg-gray-800 p-6 shadow-sm">
        <div class="text-sm text-yellow-900 dark:text-yellow-100 font-medium">需警关注</div>
        <div class="text-4xl font-extrabold text-yellow-950 dark:text-white my-2">3</div>
        <div class="text-sm text-yellow-600 dark:text-yellow-300">+1 较上周</div>
        <div class="absolute right-4 top-4 text-yellow-700 dark:text-yellow-200 text-2xl">⚠️</div>
      </div>
    </div>

    <!-- 主内容区 -->
    <div class="flex flex-col lg:flex-row gap-6 items-start">
      <!-- 模型卡片区 -->
      <div class="w-full lg:flex-[3] bg-white dark:bg-gray-800 rounded-2xl shadow p-6">
        <div class="text-xl font-bold mb-4 text-gray-800 dark:text-white">我的模型</div>
        <div class="grid grid-cols-1 sm:grid-cols-2 xl:grid-cols-3 gap-6">
          <div
            v-for="(item, idx) in modelList"
            :key="idx"
            class="bg-gray-50 dark:bg-gray-700 rounded-xl p-4 shadow">
            <div class="flex justify-between items-center mb-2">
              <span class="text-lg font-semibold text-gray-800 dark:text-white">{{ item.title }}</span>
              <span
                class="text-xs rounded-full px-3 py-1"
                :class="{
                  'bg-green-100 text-green-600': item.statusClass === 'status-running',
                  'bg-gray-200 text-gray-500': item.statusClass === 'status-paused',
                  'bg-yellow-100 text-yellow-600': item.statusClass === 'status-training'
                }">
                {{ item.status }}
              </span>
            </div>
            <div class="text-sm text-gray-600 dark:text-gray-300 space-y-1 mb-2">
              <div class="flex justify-between">
                <span>类型：{{ item.type }}</span>
                <span>准确率：{{ item.acc }}</span>
              </div>
              <div class="flex justify-between">
                <span>训练集：{{ item.dataset }}</span>
                <span>推理：{{ item.infer }}</span>
              </div>
              <div>版本：{{ item.version }}</div>
            </div>
            <div class="flex justify-end gap-2">
              <el-button size="small" type="primary">详情</el-button>
              <el-button size="small">监控</el-button>
            </div>
          </div>
        </div>
      </div>

      <!-- 右侧活动区 -->
      <div class="w-full lg:flex-[1.2] bg-white dark:bg-gray-800 rounded-2xl shadow p-6">
        <div class="text-xl font-bold mb-4 text-gray-800 dark:text-white">最新活动</div>
        <div>
          <div
            v-for="(item, idx) in activityList"
            :key="idx"
            class="flex items-center text-sm text-gray-800 dark:text-gray-300 mb-4">
            <span
              class="w-4 h-4 rounded-full inline-block mr-2"
              :class="{
                'bg-green-500': item.iconClass === 'icon-success',
                'bg-yellow-500': item.iconClass === 'icon-warning',
                'bg-red-500': item.iconClass === 'icon-error',
                'bg-blue-500': item.iconClass === 'icon-info'
              }"></span>
            <span class="flex-1">{{ item.desc }}</span>
            <span class="text-xs text-gray-400 ml-2">{{ item.time }}</span>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts" name="home">
import { ElButton } from 'element-plus';
import { activityList, modelList } from './fakeData';
</script>

<!-- Tailwind 已启用 darkMode 和基础配置 -->
