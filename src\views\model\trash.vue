<template>
  <div class="system-role-container w100 h100">
    <div class="system-role-padding layout-padding-auto layout-padding-view">
      <div class="system-user-search mb15">
        <el-input v-model="state.tableData.param.search" size="default" placeholder="请输入模型名称" style="max-width: 180px">
        </el-input>
        <el-button size="default" type="primary" class="ml10">
          <el-icon>
            <ele-Search />
          </el-icon>
          查询
        </el-button>
      </div>
      <el-table :data="state.tableData.data" v-loading="state.tableData.loading" class="table_list" style="width: 100%">
        <el-table-column type="index" label="序号" width="60" />
        <el-table-column prop="m_name" label="模型名称" show-overflow-tooltip></el-table-column>
        <el-table-column prop="version" label="版本号" show-overflow-tooltip></el-table-column>
        <el-table-column prop="m_type" label="模型分类" show-overflow-tooltip></el-table-column>
        <el-table-column prop="m_size" label="模型大小" show-overflow-tooltip></el-table-column>
        <el-table-column prop="m_status" label="模型状态" show-overflow-tooltip>
          <template #default="scope">
            <el-tag disable-transitions type="success" v-if="scope.row.status">启用</el-tag>
            <el-tag type="info" v-else disable-transitions>禁用</el-tag>
          </template>
        </el-table-column>
        <el-table-column prop="describe" label="模型描述" show-overflow-tooltip></el-table-column>
        <el-table-column prop="createTime" label="创建时间" show-overflow-tooltip></el-table-column>
        <el-table-column prop="createTime" label="删除时间" show-overflow-tooltip></el-table-column>
        <el-table-column label="操作" width="180">
          <template #default="scope">
            <!-- <el-button size="small" text type="primary" @click="onOpenEditRole('edit', scope.row)">修改</el-button> -->
            <!-- TODO -->
            <el-button size="small" text type="primary" @click="onRowRestore(scope.row)">
              <el-icon>
                <RefreshRight />
              </el-icon> 恢复
            </el-button>
            <el-button size="small" text type="danger" @click="onRowRestore(scope.row)">
              <el-icon>
                <DeleteFilled />
              </el-icon>
              彻底删除
            </el-button>
          </template>
        </el-table-column>
      </el-table>
      <el-pagination @size-change="onHandleSizeChange" @current-change="onHandleCurrentChange" class="mt15"
        :pager-count="5" :page-sizes="[10, 20, 30]" v-model:current-page="state.tableData.param.pageNum" background
        v-model:page-size="state.tableData.param.pageSize" layout="total, sizes, prev, pager, next, jumper"
        :total="state.tableData.total">
      </el-pagination>
    </div>
    <RoleDialog ref="roleDialogRef" @refresh="getTableData()" />
  </div>
</template>

<script setup lang="ts" name="systemRole">
  import { defineAsyncComponent, reactive, onMounted, ref } from "vue";
  import { ElMessageBox, ElMessage } from "element-plus";
  import { getModelList } from "@/api/modelManage";

  // 引入组件
  const RoleDialog = defineAsyncComponent(
    () => import("./components/dialogImport.vue")
  );

  // 定义变量内容
  const roleDialogRef = ref();
  const state = reactive({
    tableData: {
      data: [] as IImportModel[],
      total: 0,
      loading: false,
      param: {
        search: "",
        pageNum: 1,
        pageSize: 10,
      },
    },
  });
  // 初始化表格数据
  const getTableData = () => {
    state.tableData.loading = true;
    const data = [];
    getModelList({}).then((res) => {
      data.push(...res.data);
      state.tableData.data = data;
      state.tableData.total = state.tableData.data.length;
      state.tableData.loading = false;
    });
  };
  // 打开新增模型弹窗
  const onOpenAddRole = (type: string) => {
    roleDialogRef.value.openDialog(type);
  };
  // 打开修改模型弹窗
  const onOpenEditRole = (type: string, row: Object) => {
    roleDialogRef.value.openDialog(type, row);
  };
  // 删除模型
  const onRowRestore = (row: IImportModel) => {
    ElMessageBox.confirm(
      `此操作将恢复模型名称：“${row.m_name}”，是否继续?`,
      "提示",
      {
        confirmButtonText: "确认",
        cancelButtonText: "取消",
        type: "warning",
      }
    )
      .then(() => {
        getTableData();
        ElMessage.success("恢复成功");
      })
      .catch(() => { });
  };
  // 分页改变
  const onHandleSizeChange = (val: number) => {
    state.tableData.param.pageSize = val;
    getTableData();
  };
  // 分页改变
  const onHandleCurrentChange = (val: number) => {
    state.tableData.param.pageNum = val;
    getTableData();
  };
  // 页面加载时
  onMounted(() => {
    getTableData();
  });
</script>

<style scoped lang="scss">
  .system-role-container {
    .system-role-padding {
      padding: 15px;

      .el-table {
        flex: 1;
      }
    }

    .table_list {
      height: calc(100vh - 250px);
    }
  }
</style>
