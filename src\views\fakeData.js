export const modelList = [
  {
    title: 'GPT-4 文本生成',
    status: '运行中',
    statusClass: 'status-running',
    type: '文本生成',
    acc: '98.6%',
    dataset: '12k',
    infer: '1.2k',
    version: 'v2.1',
  },
  {
    title: 'ResNet-50 图像分类',
    status: '运行中',
    statusClass: 'status-running',
    type: '图像分类',
    acc: '89.7%',
    dataset: '8.5k',
    infer: '856',
    version: 'v3.8',
  },
  {
    title: 'BERT 情感分析',
    status: '暂停',
    statusClass: 'status-paused',
    type: '文本分析',
    acc: '91.3%',
    dataset: '4.3k',
    infer: '432',
    version: 'v1.5',
  },
  {
    title: 'YOLO 目标检测',
    status: '运行中',
    statusClass: 'status-running',
    type: '目标检测',
    acc: '87.9%',
    dataset: '2.1k',
    infer: '1.3k',
    version: 'v2.1',
  },
  {
    title: 'Transformer 翻译',
    status: '训练中',
    statusClass: 'status-training',
    type: '翻译',
    acc: '98.2%',
    dataset: '8.7k',
    infer: '1.1k',
    version: 'v2.3',
  },
  {
    title: 'CNN 医学影像',
    status: '运行中',
    statusClass: 'status-running',
    type: '医学影像',
    acc: '96.8%',
    dataset: '234',
    infer: '3.9k',
    version: 'v1.2',
  },
];

export const activityList = [
  {
    iconClass: 'icon-success',
    desc: '模型部署成功 GPT-4 文本生成',
    time: '2分钟前',
  },
  {
    iconClass: 'icon-success',
    desc: '训练任务完成 ResNet-50 图像分类',
    time: '15分钟前',
  },
  {
    iconClass: 'icon-warning',
    desc: '健康状态警告 BERT 情感分析',
    time: '1小时前',
  },
  {
    iconClass: 'icon-error',
    desc: '训练任务失败 CNN 医学影像',
    time: '1小时前',
  },
  {
    iconClass: 'icon-info',
    desc: '模型版本更新 YOLO 目标检测',
    time: '3小时前',
  },
];
