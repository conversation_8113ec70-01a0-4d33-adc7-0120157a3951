<script setup lang="ts">
  import { ref, reactive } from "vue"
  import { ElMessage } from 'element-plus'

  const tableColumnHeader = [
    { label: "文件名", prop: "fileName", align: "center" },
    { label: "文件类型", prop: "fileType", align: "center" },
    { label: "文件大小", prop: "fileSize", align: "center" },
    { label: "上传人", prop: "uploader", align: "center" },
    { label: "上传时间", prop: "uploadTime", align: "center" },
    { label: "校验状态", prop: "validateStatus", align: "center" },
    { label: "重复检测", prop: "duplicateStatus", align: "center" },
    { label: "上传状态", prop: "uploadStatus", align: "center" },
  ]

  const state = reactive({
    tableTotal: 0,
    tableList: [
      {
        fileName: 'image_model_v1.tar.gz',
        fileType: 'tar.gz',
        fileSize: '120MB',
        uploader: '张三',
        uploadTime: '2023-09-01 09:00:00',
        validateStatus: '通过',
        duplicateStatus: '无重复',
        uploadStatus: '已上传'
      },
      {
        fileName: 'audio_model_v2.zip',
        fileType: 'zip',
        fileSize: '98MB',
        uploader: '李四',
        uploadTime: '2023-09-02 10:30:00',
        validateStatus: '格式错误',
        duplicateStatus: '无重复',
        uploadStatus: '上传失败'
      },
      {
        fileName: 'image_model_v1.tar.gz',
        fileType: 'tar.gz',
        fileSize: '120MB',
        uploader: '王五',
        uploadTime: '2023-09-03 11:00:00',
        validateStatus: '通过',
        duplicateStatus: '重复文件',
        uploadStatus: '上传失败'
      }
      // ...可添加更多示例...
    ],
    params: {
      page: 1,
      pageSize: 10
    }
  })
  // 表格操作
  const tableChangeSelect = (val: any) => {
    console.log(val)
  }
  // 表格删除
  const tableDelete = (row: any) => {
    ElMessage.success('删除成功')
  }
  // 表格编辑
  const tableEdit = (row: any) => {
    console.log(row)
  }
  function handleFilter({ type, data }) {
    console.log('筛选条件:', data)
    // 调用查询接口
  }


</script>
<template>
  <section class="mode_list p-5">
    <!-- <TableFilter :filterList="filters" :clearable="true" @submit="handleFilter" /> -->
    <!-- 表格 -->
    <TableColumn :data="state.tableList" :total="state.tableTotal" :column="tableColumnHeader"
      :page.sync="state.params.page" :pageSize.sync="state.params.pageSize" @changeSelect="tableChangeSelect">
      <template #setting="{ row }">
        <el-button @click="tableDelete(row)" type="primary" link size="small">删除</el-button>
        <el-button @click="tableEdit(row)" type="primary" link size="small">编辑</el-button>
      </template>
    </TableColumn>

  </section>
</template>
<style lang="scss" scoped></style>
